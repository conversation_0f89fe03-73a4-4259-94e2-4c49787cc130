<!DOCTYPE html>
<html>
<head>
  <title>VidCompressor</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      width: 400px;
      min-height: 500px;
      background: #fff;
      color: #202124;
      line-height: 1.5;
    }

    .header {
      background: #1a73e8;
      color: white;
      padding: 16px 20px;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .header h1 {
      font-size: 18px;
      font-weight: 500;
      margin: 0;
    }

    .header .material-icons {
      font-size: 24px;
    }

    .container {
      padding: 20px;
    }

    .auth-container {
      text-align: center;
      padding: 40px 20px;
    }

    .auth-container h2 {
      font-size: 20px;
      font-weight: 400;
      margin-bottom: 8px;
      color: #202124;
    }

    .auth-container p {
      color: #5f6368;
      margin-bottom: 32px;
      font-size: 14px;
    }

    .google-btn {
      display: inline-flex;
      align-items: center;
      gap: 12px;
      background: #1a73e8;
      color: white;
      border: none;
      border-radius: 24px;
      padding: 12px 24px;
      font-family: 'Google Sans', sans-serif;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;
      min-width: 200px;
      justify-content: center;
    }

    .google-btn:hover {
      background: #1557b0;
      box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
    }

    .google-btn:active {
      background: #1246a0;
    }

    .google-btn .material-icons {
      font-size: 18px;
    }

    .logout-btn {
      background: transparent;
      color: #1a73e8;
      border: 1px solid #dadce0;
      border-radius: 20px;
      padding: 8px 16px;
      font-family: 'Google Sans', sans-serif;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-bottom: 20px;
    }

    .logout-btn:hover {
      background: #f8f9fa;
      border-color: #1a73e8;
    }

    .videos-section h2 {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
      color: #202124;
    }

    .video-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 0;
      border-bottom: 1px solid #e8eaed;
    }

    .video-item:last-child {
      border-bottom: none;
    }

    .video-info {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .video-icon {
      color: #5f6368;
      font-size: 20px;
    }

    .video-name {
      font-size: 14px;
      color: #202124;
      font-weight: 400;
    }

    .compress-btn {
      background: #34a853;
      color: white;
      border: none;
      border-radius: 16px;
      padding: 6px 16px;
      font-family: 'Google Sans', sans-serif;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .compress-btn:hover {
      background: #2d8f47;
    }

    .compress-btn:disabled {
      background: #dadce0;
      color: #5f6368;
      cursor: not-allowed;
    }

    .status {
      margin-top: 16px;
      padding: 12px;
      border-radius: 8px;
      font-size: 14px;
      text-align: center;
    }

    .status.success {
      background: #e8f5e8;
      color: #137333;
      border: 1px solid #ceead6;
    }

    .status.error {
      background: #fce8e6;
      color: #d93025;
      border: 1px solid #f9dedc;
    }

    .status.info {
      background: #e8f0fe;
      color: #1a73e8;
      border: 1px solid #d2e3fc;
    }

    .loading {
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }

    .spinner {
      width: 16px;
      height: 16px;
      border: 2px solid #e8eaed;
      border-top: 2px solid #1a73e8;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #5f6368;
    }

    .empty-state .material-icons {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }
  </style>
</head>
<body>
  <div class="header">
    <span class="material-icons">video_library</span>
    <h1>VidCompressor</h1>
  </div>

  <div id="auth-container" class="container">
    <div class="auth-container">
      <h2>Welcome to VidCompressor</h2>
      <p>Sign in with your Google account to compress videos from Google Photos</p>
      <button id="login" class="google-btn">
        <span class="material-icons">account_circle</span>
        Sign in with Google
      </button>
      <div id="auth-status" class="status" style="display: none;"></div>
    </div>
  </div>

  <div id="main-container" class="container" style="display: none;">
    <button id="logout" class="logout-btn">Sign out</button>

    <div class="videos-section">
      <h2>Your Videos</h2>
      <div id="videos-list"></div>
      <div id="empty-state" class="empty-state" style="display: none;">
        <div class="material-icons">video_library</div>
        <p>No videos found in your Google Photos</p>
      </div>
    </div>

    <div id="status" class="status" style="display: none;"></div>
  </div>

  <script src="popup.js"></script>
</body>
</html>

version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=vidcompressor
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=vidcompressor
    volumes:
      - postgres_data:/var/lib/postgresql/data/

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  worker:
    build:
      context: ./worker
      dockerfile: Dockerfile
    depends_on:
      - postgres
      - redis

volumes:
  postgres_data:

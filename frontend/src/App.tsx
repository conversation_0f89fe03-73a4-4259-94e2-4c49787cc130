import React, { useState, useEffect } from 'react';
import { GoogleOAuthProvider, GoogleLogin, CredentialResponse } from '@react-oauth/google';
import { AppBar, Toolbar, Typography, Button, Container, Grid, Paper } from '@mui/material';
import * as signalR from "@microsoft/signalr";
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import Subscribe from './Subscribe';

const App: React.FC = () => {
  const [token, setToken] = useState<string | null>(localStorage.getItem('jwt'));
  const [videos, setVideos] = useState<string[]>([]);
  const [notification, setNotification] = useState<string | null>(null);

  useEffect(() => {
    const connection = new signalR.HubConnectionBuilder()
      .withUrl("/notificationHub")
      .build();

    connection.on("ReceiveMessage", (user, message) => {
      setNotification(`${user}: ${message}`);
    });

    connection.start().catch(err => console.error(err));

    if (token) {
      fetch('/api/videos', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(res => res.json())
      .then(data => setVideos(data));
    }

    return () => {
      connection.stop();
    };
  }, [token]);

  const handleLoginSuccess = (credentialResponse: CredentialResponse) => {
    fetch('/api/auth/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token: credentialResponse.credential })
    })
    .then(res => res.json())
    .then(data => {
      localStorage.setItem('jwt', data.token);
      setToken(data.token);
    });
  };

  const handleLoginError = () => {
    console.log('Login Failed');
  };

  const handleLogout = () => {
    localStorage.removeItem('jwt');
    setToken(null);
    setVideos([]);
  };

  const handleCompress = (mediaItemId: string) => {
    fetch(`/api/videos/${mediaItemId}/compress`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
  };

  return (
    <GoogleOAuthProvider clientId="YOUR_GOOGLE_CLIENT_ID">
      <Router>
        <AppBar position="static">
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              VidCompressor
            </Typography>
            {token ? (
              <Button color="inherit" onClick={handleLogout}>Logout</Button>
            ) : (
              <GoogleLogin onSuccess={handleLoginSuccess} onError={handleLoginError} />
            )}
          </Toolbar>
        </AppBar>
        <Container sx={{ mt: 4 }}>
          {notification && <Typography color="error">{notification}</Typography>}
          <Routes>
            <Route path="/" element={
              token ? (
                <div>
                  <Typography variant="h4" gutterBottom>
                    Your Video Library
                  </Typography>
                  <Subscribe />
                  <Grid container spacing={2} sx={{ mt: 2 }}>
                    {videos.map((video, index) => (
                      <Grid xs={12} sm={6} md={4} key={index}>
                        <Paper sx={{ p: 2 }}>
                          <Typography>{video}</Typography>
                          <Button onClick={() => handleCompress(video)}>Compress</Button>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </div>
              ) : (
                <Typography variant="h5" align="center" sx={{ mt: 10 }}>
                  Please sign in with your Google account to continue.
                </Typography>
              )
            } />
            <Route path="/success" element={<Typography variant="h4">Thanks for subscribing!</Typography>} />
            <Route path="/cancel" element={<Typography variant="h4">Subscription canceled.</Typography>} />
          </Routes>
        </Container>
      </Router>
    </GoogleOAuthProvider>
  );
};

export default App;

document.addEventListener('DOMContentLoaded', function() {
  const loginButton = document.getElementById('login');
  const logoutButton = document.getElementById('logout');
  const authContainer = document.getElementById('auth-container');
  const mainContainer = document.getElementById('main-container');
  const videosList = document.getElementById('videos-list');
  const statusDiv = document.getElementById('status');
  const authStatusDiv = document.getElementById('auth-status');
  const emptyState = document.getElementById('empty-state');

  let googleToken = null;
  let jwtToken = null;

  // Check for existing JWT token on startup
  chrome.storage.local.get(['jwtToken'], function(result) {
    if (result.jwtToken) {
      jwtToken = result.jwtToken;
      console.log('Found existing JWT token');
      showMainContainer();
      fetchVideos();
    }
  });

  loginButton.addEventListener('click', function() {
    console.log('Login button clicked');
    showAuthStatus('Signing in with Google...', 'info');
    loginButton.disabled = true;
    loginButton.innerHTML = '<div class="loading"><div class="spinner"></div>Signing in...</div>';

    // Step 1: Get Google token
    chrome.identity.getAuthToken({ interactive: true }, function(token) {
      if (chrome.runtime.lastError) {
        console.error('Google login failed:', chrome.runtime.lastError);
        showAuthStatus('Google sign in failed: ' + chrome.runtime.lastError.message, 'error');
        resetLoginButton();
        return;
      }

      googleToken = token;
      console.log('Google login successful');
      showAuthStatus('Authenticating with VidCompressor...', 'info');

      // Step 2: Authenticate with your backend
      authenticateWithBackend(token);
    });
  });

  function authenticateWithBackend(googleToken) {
    fetch('http://localhost:5119/auth/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token: googleToken })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then(data => {
      jwtToken = data.token;
      console.log('Backend authentication successful');

      // Store JWT token for future use
      chrome.storage.local.set({ jwtToken: jwtToken }, function() {
        showAuthStatus('Sign in successful!', 'success');

        // Test the authentication by fetching user info
        testAuthentication().then(() => {
          setTimeout(() => {
            showMainContainer();
            fetchVideos();
          }, 1000);
        });
      });
    })
    .catch(error => {
      console.error('Backend authentication failed:', error);
      showAuthStatus('Authentication failed: ' + error.message, 'error');
      resetLoginButton();
    });
  }

  function resetLoginButton() {
    loginButton.disabled = false;
    loginButton.innerHTML = '<span class="material-icons">account_circle</span>Sign in with Google';
  }

  function testAuthentication() {
    return fetch('http://localhost:5119/auth/me', {
      headers: {
        'Authorization': 'Bearer ' + jwtToken
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`Authentication test failed: ${response.status}`);
      }
      return response.json();
    })
    .then(userData => {
      console.log('User authenticated successfully:', userData);
      return userData;
    })
    .catch(error => {
      console.error('Authentication test failed:', error);
      throw error;
    });
  }

  logoutButton.addEventListener('click', function() {
    console.log('Logging out...');

    // Clear JWT token from storage
    chrome.storage.local.remove(['jwtToken'], function() {
      console.log('JWT token removed from storage');
    });

    // Revoke Google token if we have it
    if (googleToken) {
      fetch(`https://oauth2.googleapis.com/revoke?token=${googleToken}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
      .then(() => {
        console.log('Google token revoked');
      })
      .catch(error => {
        console.warn('Error revoking Google token:', error);
      })
      .finally(() => {
        // Remove the cached token from Chrome
        chrome.identity.removeCachedAuthToken({ token: googleToken }, function() {
          console.log('Google token removed from Chrome cache');
        });
      });
    }

    // Clear all tokens and UI state
    googleToken = null;
    jwtToken = null;
    showAuthContainer();
    hideStatus();

    // Clear the videos list
    videosList.innerHTML = '';
    emptyState.style.display = 'none';
  });

  function showAuthContainer() {
    authContainer.style.display = 'block';
    mainContainer.style.display = 'none';
    hideAuthStatus();

    // Reset login button state
    loginButton.disabled = false;
    loginButton.innerHTML = '<span class="material-icons">account_circle</span>Sign in with Google';
  }

  function showMainContainer() {
    authContainer.style.display = 'none';
    mainContainer.style.display = 'block';
  }

  function showAuthStatus(message, type) {
    authStatusDiv.textContent = message;
    authStatusDiv.className = `status ${type}`;
    authStatusDiv.style.display = 'block';
  }

  function hideAuthStatus() {
    authStatusDiv.style.display = 'none';
  }

  function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
  }

  function hideStatus() {
    statusDiv.style.display = 'none';
  }

  function fetchVideos() {
    hideStatus();
    showStatus('Loading your videos...', 'info');

    fetch('http://localhost:5119/api/videos', {
      headers: {
        'Authorization': 'Bearer ' + jwtToken
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then(videos => {
      hideStatus();
      videosList.innerHTML = '';

      if (!videos || videos.length === 0) {
        emptyState.style.display = 'block';
        return;
      }

      emptyState.style.display = 'none';
      videos.forEach(video => {
        const videoItem = document.createElement('div');
        videoItem.className = 'video-item';

        const videoInfo = document.createElement('div');
        videoInfo.className = 'video-info';

        const videoIcon = document.createElement('span');
        videoIcon.className = 'material-icons video-icon';
        videoIcon.textContent = 'videocam';
        videoInfo.appendChild(videoIcon);

        const videoName = document.createElement('span');
        videoName.className = 'video-name';
        videoName.textContent = video.length > 30 ? video.substring(0, 30) + '...' : video;
        videoName.title = video;
        videoInfo.appendChild(videoName);

        videoItem.appendChild(videoInfo);

        const compressButton = document.createElement('button');
        compressButton.className = 'compress-btn';
        compressButton.textContent = 'Compress';
        compressButton.addEventListener('click', function() {
          compressVideo(video, compressButton);
        });
        videoItem.appendChild(compressButton);

        videosList.appendChild(videoItem);
      });
    })
    .catch(error => {
      console.error('Error fetching videos:', error);
      showStatus('Error loading videos: ' + error.message, 'error');
      emptyState.style.display = 'none';
    });
  }

  function compressVideo(mediaItemId, button) {
    const originalText = button.textContent;
    button.disabled = true;
    button.innerHTML = '<div class="loading"><div class="spinner"></div></div>';

    showStatus(`Starting compression for ${mediaItemId.substring(0, 20)}...`, 'info');

    fetch(`http://localhost:5119/api/videos/${mediaItemId}/compress`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + jwtToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ quality: 'medium' })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then(data => {
      showStatus(data.message || 'Compression started successfully!', 'success');
      button.textContent = 'Queued';
      button.disabled = true;
    })
    .catch(error => {
      console.error('Error compressing video:', error);
      showStatus('Error starting compression: ' + error.message, 'error');
      button.disabled = false;
      button.textContent = originalText;
    });
  }
});

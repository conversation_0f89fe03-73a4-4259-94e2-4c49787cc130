using Microsoft.AspNetCore.SignalR.Client;
using StackExchange.Redis;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using VidCompressor.Services;

namespace worker;

public class Worker : BackgroundService
{
    private readonly ILogger<Worker> _logger;
    private readonly IConnectionMultiplexer _redis;
    private readonly HubConnection _hubConnection;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly GooglePhotosService _googlePhotosService;
    private readonly IConfiguration _configuration;
    private static readonly ActivitySource Activity = new ActivitySource("VidCompressor.Worker");

    public Worker(ILogger<Worker> logger, IConnectionMultiplexer redis, IHttpClientFactory httpClientFactory, GooglePhotosService googlePhotosService, IConfiguration configuration)
    {
        _logger = logger;
        _redis = redis;
        _httpClientFactory = httpClientFactory;
        _googlePhotosService = googlePhotosService;
        _configuration = configuration;

        var backendUrl = _configuration["BackendUrl"] ?? "http://localhost:5000";
        _hubConnection = new HubConnectionBuilder()
            .WithUrl($"{backendUrl}/notificationHub")
            .Build();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        await _hubConnection.StartAsync(stoppingToken);

        var subscriber = _redis.GetSubscriber();
        await subscriber.SubscribeAsync("video-compression-jobs", async (channel, message) =>
        {
            using (var activity = Activity.StartActivity("ProcessVideoJob"))
            {
                var messageParts = message.ToString().Split(':');
                var accessToken = messageParts[0];
                var mediaItemId = messageParts[1];
                var quality = messageParts.Length > 2 ? messageParts[2] : "medium";

                _logger.LogInformation("Received job for media item: {mediaItemId} with quality: {quality}", mediaItemId, quality);
                activity?.SetTag("job.id", mediaItemId);
                activity?.SetTag("job.quality", quality);

                var httpClient = _httpClientFactory.CreateClient();
                var backendUrl = _configuration["BackendUrl"] ?? "http://localhost:5000";
                var videoStream = await httpClient.GetStreamAsync($"{backendUrl}/api/videos/{mediaItemId}/download");

                var inputPath = Path.Combine(Path.GetTempPath(), $"{mediaItemId}.mp4");
                var outputPath = Path.Combine(Path.GetTempPath(), $"compressed-{mediaItemId}.mp4");

                await using (var fileStream = new FileStream(inputPath, FileMode.Create, FileAccess.Write))
                {
                    await videoStream.CopyToAsync(fileStream);
                }

                // Map quality to CRF values
                var crfValue = quality switch
                {
                    "high" => "18",    // High quality, larger file
                    "medium" => "23",  // Balanced quality and size
                    "low" => "28",     // Lower quality, smaller file
                    _ => "23"          // Default to medium
                };

                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "ffmpeg",
                        Arguments = $"-i {inputPath} -vcodec libx264 -crf {crfValue} {outputPath}",
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true,
                    }
                };

                process.Start();
                await process.WaitForExitAsync(stoppingToken);

                _logger.LogInformation("Finished processing job for media item: {mediaItemId} with quality: {quality}", mediaItemId, quality);

                await _googlePhotosService.UploadVideoAsync(accessToken, outputPath);

                await _hubConnection.SendAsync("SendMessage", "Worker", $"Job for {mediaItemId} complete (quality: {quality})", stoppingToken);
            }
        });

        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay(1000, stoppingToken);
        }

        await _hubConnection.StopAsync(stoppingToken);
    }
}


document.addEventListener('DOMContentLoaded', function() {
  const loginButton = document.getElementById('login');
  const logoutButton = document.getElementById('logout');
  const authContainer = document.getElementById('auth-container');
  const mainContainer = document.getElementById('main-container');
  const videosList = document.getElementById('videos-list');
  const statusDiv = document.getElementById('status');
  const authStatusDiv = document.getElementById('auth-status');
  const emptyState = document.getElementById('empty-state');

  let authToken = null;

  // Check for existing token on startup
  chrome.identity.getAuthToken({ interactive: false }, function(token) {
    if (chrome.runtime.lastError) {
      console.log('No existing token:', chrome.runtime.lastError.message);
      return;
    }
    authToken = token;
    console.log('Found existing token:', token);
    showMainContainer();
    fetchVideos();
  });

  loginButton.addEventListener('click', function() {
    console.log('Login button clicked');
    showAuthStatus('Signing in...', 'info');
    loginButton.disabled = true;
    loginButton.innerHTML = '<div class="loading"><div class="spinner"></div>Signing in...</div>';

    chrome.identity.getAuthToken({ interactive: true }, function(token) {
      loginButton.disabled = false;
      loginButton.innerHTML = '<span class="material-icons">account_circle</span>Sign in with Google';

      if (chrome.runtime.lastError) {
        console.error('Login failed:', chrome.runtime.lastError);
        showAuthStatus('Sign in failed: ' + chrome.runtime.lastError.message, 'error');
        return;
      }
      authToken = token;
      console.log('Login successful, token:', token);
      showAuthStatus('Sign in successful!', 'success');
      setTimeout(() => {
        showMainContainer();
        fetchVideos();
      }, 1000);
    });
  });

  logoutButton.addEventListener('click', function() {
    if (authToken) {
      console.log('Logging out...');

      // First revoke the token with Google
      fetch(`https://oauth2.googleapis.com/revoke?token=${authToken}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
      .then(() => {
        console.log('Token revoked with Google');
      })
      .catch(error => {
        console.warn('Error revoking token with Google:', error);
        // Continue with logout even if revocation fails
      })
      .finally(() => {
        // Remove the cached token from Chrome
        chrome.identity.removeCachedAuthToken({ token: authToken }, function() {
          console.log('Token removed from Chrome cache');
          authToken = null;
          showAuthContainer();
          hideStatus();

          // Clear the videos list
          videosList.innerHTML = '';
          emptyState.style.display = 'none';
        });
      });
    }
  });

  function showAuthContainer() {
    authContainer.style.display = 'block';
    mainContainer.style.display = 'none';
    hideAuthStatus();

    // Reset login button state
    loginButton.disabled = false;
    loginButton.innerHTML = '<span class="material-icons">account_circle</span>Sign in with Google';
  }

  function showMainContainer() {
    authContainer.style.display = 'none';
    mainContainer.style.display = 'block';
  }

  function showAuthStatus(message, type) {
    authStatusDiv.textContent = message;
    authStatusDiv.className = `status ${type}`;
    authStatusDiv.style.display = 'block';
  }

  function hideAuthStatus() {
    authStatusDiv.style.display = 'none';
  }

  function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
  }

  function hideStatus() {
    statusDiv.style.display = 'none';
  }

  function fetchVideos() {
    hideStatus();
    showStatus('Loading your videos...', 'info');

    fetch('http://localhost:5119/api/videos', {
      headers: {
        'Authorization': 'Bearer ' + authToken
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then(videos => {
      hideStatus();
      videosList.innerHTML = '';

      if (!videos || videos.length === 0) {
        emptyState.style.display = 'block';
        return;
      }

      emptyState.style.display = 'none';
      videos.forEach(video => {
        const videoItem = document.createElement('div');
        videoItem.className = 'video-item';

        const videoInfo = document.createElement('div');
        videoInfo.className = 'video-info';

        const videoIcon = document.createElement('span');
        videoIcon.className = 'material-icons video-icon';
        videoIcon.textContent = 'videocam';
        videoInfo.appendChild(videoIcon);

        const videoName = document.createElement('span');
        videoName.className = 'video-name';
        videoName.textContent = video.length > 30 ? video.substring(0, 30) + '...' : video;
        videoName.title = video;
        videoInfo.appendChild(videoName);

        videoItem.appendChild(videoInfo);

        const compressButton = document.createElement('button');
        compressButton.className = 'compress-btn';
        compressButton.textContent = 'Compress';
        compressButton.addEventListener('click', function() {
          compressVideo(video, compressButton);
        });
        videoItem.appendChild(compressButton);

        videosList.appendChild(videoItem);
      });
    })
    .catch(error => {
      console.error('Error fetching videos:', error);
      showStatus('Error loading videos: ' + error.message, 'error');
      emptyState.style.display = 'none';
    });
  }

  function compressVideo(mediaItemId, button) {
    const originalText = button.textContent;
    button.disabled = true;
    button.innerHTML = '<div class="loading"><div class="spinner"></div></div>';

    showStatus(`Starting compression for ${mediaItemId.substring(0, 20)}...`, 'info');

    fetch(`http://localhost:5119/api/videos/${mediaItemId}/compress`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + authToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ quality: 'medium' })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then(data => {
      showStatus(data.message || 'Compression started successfully!', 'success');
      button.textContent = 'Queued';
      button.disabled = true;
    })
    .catch(error => {
      console.error('Error compressing video:', error);
      showStatus('Error starting compression: ' + error.message, 'error');
      button.disabled = false;
      button.textContent = originalText;
    });
  }
});

resource "google_container_cluster" "primary" {
  name     = "vidcompressor-cluster"
  location = var.region

  # Enable Autopilot
  enable_autopilot = true
}

resource "google_artifact_registry_repository" "registry" {
  location      = var.region
  repository_id = "vidcompressor-registry"
  format        = "DOCKER"
}

resource "google_sql_database_instance" "postgres" {
  name             = "vidcompressor-db"
  database_version = "POSTGRES_15"
  region           = var.region

  settings {
    tier = "db-g1-small"
  }
}

resource "google_redis_instance" "redis" {
  name           = "vidcompressor-redis"
  tier           = "BASIC"
  memory_size_gb = 1
  region         = var.region
}

resource "google_secret_manager_secret" "google_client_id" {
  secret_id = "google-client-id"

  replication {
    automatic = true
  }
}

resource "google_secret_manager_secret_version" "google_client_id_value" {
  secret      = google_secret_manager_secret.google_client_id.id
  secret_data = "546390650743-1afjtkga4c7i380gh7d1sp5mpje9kgsl.apps.googleusercontent.com"
}

resource "helm_release" "vidcompressor" {
  name       = "vidcompressor"
  chart      = "../helm/vidcompressor"

  values = [
    file("../helm/vidcompressor/values.yaml")
  ]

  set {
    name  = "frontend.image.repository"
    value = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.registry.repository_id}/frontend"
  }

  set {
    name  = "backend.image.repository"
    value = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.registry.repository_id}/backend"
  }

  set {
    name  = "worker.image.repository"
    value = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.registry.repository_id}/worker"
  }
}

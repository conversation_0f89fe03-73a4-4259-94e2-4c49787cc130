# Local Development Setup

This guide helps you run your VidCompressor backend, worker, database, and Redis locally for end-to-end testing without cloud hosting costs.

## Prerequisites

- Docker and Docker Compose
- .NET 8 SDK (for Option 1)
- Chrome browser with your extension

## Option 1: Mixed Development (Recommended)

Run infrastructure in Docker, .NET services locally for easier debugging:

```bash
# Start infrastructure
./run-local-dev.sh

# In separate terminals:
cd backend && dotnet run
cd worker && dotnet run
```

**Pros:**
- Easy debugging with breakpoints
- Fast rebuilds
- Direct access to logs
- Hot reload support

## Option 2: Full Docker

Run everything in Docker containers:

```bash
./run-local-docker.sh
```

**Pros:**
- Matches production environment
- No local .NET SDK required
- Consistent across different machines

## Configuration

### Chrome Extension
Update your Chrome extension's `manifest.json` to point to local backend:
```json
"host_permissions": [
  "http://localhost:5000/*",  // or 5119 for Option 1
  "https://photos.google.com/*"
]
```

### Environment Variables
The setup uses these default values:
- **Database**: `vidcompressor:password@localhost:5432/vidcompressor`
- **Redis**: `localhost:6379`
- **Backend**: `http://localhost:5000` (Docker) or `http://localhost:5119` (local)

### API Keys
Make sure to set your API keys in `backend/appsettings.Development.json`:
- Google Client ID
- Stripe keys (if testing payments)

## Testing Your Setup

1. **Database Connection**: Check if backend starts without errors
2. **Redis Connection**: Check if worker connects to Redis queue
3. **Chrome Extension**: Test authentication and video selection
4. **End-to-End**: Submit a compression job and verify worker processes it

## Troubleshooting

### Services won't start
```bash
# Check service logs
docker-compose logs postgres
docker-compose logs redis

# Restart services
docker-compose restart
```

### Worker can't connect to backend
- Verify backend is running and accessible
- Check BackendUrl environment variable in docker-compose.yml

### Database connection issues
- Ensure PostgreSQL is fully started (check health status)
- Verify connection string matches docker-compose settings

## Stopping Services

```bash
# Stop infrastructure only (Option 1)
docker-compose down

# Stop all Docker services (Option 2)  
docker-compose down

# Stop .NET services: Ctrl+C in their terminals
```

## Development Workflow

1. Start infrastructure: `./run-local-dev.sh`
2. Run backend: `cd backend && dotnet run`
3. Run worker: `cd worker && dotnet run`
4. Load Chrome extension in developer mode
5. Test compression workflow
6. Make code changes and restart services as needed

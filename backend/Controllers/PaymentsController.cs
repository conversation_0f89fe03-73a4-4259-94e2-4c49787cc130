using Microsoft.AspNetCore.Mvc;
using Stripe.Checkout;
using System.Collections.Generic;

[Route("api/[controller]")]
public class PaymentsController : Controller
{
    [HttpPost("create-checkout-session")]
    public ActionResult CreateCheckoutSession()
    {
        var options = new SessionCreateOptions
        {
            LineItems = new List<SessionLineItemOptions>
            {
                new SessionLineItemOptions
                {
                    Price = "{{PRICE_ID}}", // Replace with your price ID
                    Quantity = 1,
                },
            },
            Mode = "subscription",
            SuccessUrl = "http://localhost:3000/success",
            CancelUrl = "http://localhost:3000/cancel",
        };

        var service = new SessionService();
        Session session = service.Create(options);

        return Json(new { id = session.Id });
    }
}

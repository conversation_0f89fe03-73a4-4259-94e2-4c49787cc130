frontend:
  replicaCount: 1
  image:
    repository: gcr.io/your-project-id/frontend
    tag: latest
  service:
    type: LoadBalancer
    port: 80
    targetPort: 3000

backend:
  replicaCount: 1
  image:
    repository: gcr.io/your-project-id/backend
    tag: latest
  service:
    type: ClusterIP
    port: 80
    targetPort: 5000

worker:
  replicaCount: 1
  image:
    repository: gcr.io/your-project-id/worker
    tag: latest

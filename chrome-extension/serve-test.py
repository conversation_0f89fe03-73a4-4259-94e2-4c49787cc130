#!/usr/bin/env python3

"""
Simple HTTP server with CORS and SharedArrayBuffer headers for testing FFmpeg WebAssembly
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

PORT = 3000

class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Enable SharedArrayBuffer support
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        # Default to test.html for root path
        if self.path == '/':
            self.path = '/test.html'
        return super().do_GET()
    
    def log_message(self, format, *args):
        # Custom log format
        print(f"📡 {args[0]} - {args[1]} - {args[2]}")

def main():
    # Change to the script directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        with socketserver.TCPServer(("", PORT), CORSRequestHandler) as httpd:
            print(f"🚀 Test server running at http://localhost:{PORT}")
            print(f"📝 Open http://localhost:{PORT} to test FFmpeg compression")
            print(f"✅ SharedArrayBuffer headers enabled for better performance")
            print(f"🛑 Press Ctrl+C to stop the server")
            print()
            
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Shutting down server...")
        print("✅ Server stopped")
        sys.exit(0)
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {PORT} is already in use. Try a different port or stop the existing server.")
        else:
            print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

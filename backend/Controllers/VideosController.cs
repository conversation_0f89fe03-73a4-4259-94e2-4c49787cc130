using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StackExchange.Redis;
using System.Threading.Tasks;
using VidCompressor.Services;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class VideosController : ControllerBase
{
    private readonly GooglePhotosService _googlePhotosService;
    private readonly IConnectionMultiplexer _redis;

    public VideosController(GooglePhotosService googlePhotosService, IConnectionMultiplexer redis)
    {
        _googlePhotosService = googlePhotosService;
        _redis = redis;
    }

    [HttpGet]
    public async Task<IActionResult> GetVideos()
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
        var videos = await _googlePhotosService.GetVideosAsync(accessToken);
        return Ok(videos);
    }

    [HttpGet("{mediaItemId}/info")]
    public async Task<IActionResult> GetVideoInfo(string mediaItemId)
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
        var videoInfo = await _googlePhotosService.GetVideoInfoAsync(accessToken, mediaItemId);
        return Ok(videoInfo);
    }

    [HttpGet("{mediaItemId}/download")]
    public async Task<IActionResult> DownloadVideo(string mediaItemId)
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
        var videoStream = await _googlePhotosService.DownloadVideoAsync(accessToken, mediaItemId);
        return File(videoStream, "video/mp4", "video.mp4");
    }

    [HttpPost("{mediaItemId}/compress")]
    public async Task<IActionResult> CompressVideo(string mediaItemId, [FromBody] CompressVideoRequest request)
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
        var quality = request?.Quality ?? "medium";
        var subscriber = _redis.GetSubscriber();
        await subscriber.PublishAsync(RedisChannel.Literal("video-compression-jobs"), $"{accessToken}:{mediaItemId}:{quality}");
        return Ok(new { message = "Video compression started" });
    }
}

public class CompressVideoRequest
{
    public string Quality { get; set; } = "medium";
}


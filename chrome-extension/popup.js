document.addEventListener('DOMContentLoaded', function() {
  const loginButton = document.getElementById('login');
  const logoutButton = document.getElementById('logout');
  const authContainer = document.getElementById('auth-container');
  const mainContainer = document.getElementById('main-container');
  const videosList = document.getElementById('videos-list');
  const statusDiv = document.getElementById('status');

  let authToken = null;

  // Check for existing token on startup
  chrome.identity.getAuthToken({ interactive: false }, function(token) {
    if (chrome.runtime.lastError) {
      console.log('No existing token:', chrome.runtime.lastError.message);
      return;
    }
    authToken = token;
    console.log('Found existing token:', token);
    showMainContainer();
    fetchVideos();
  });

  loginButton.addEventListener('click', function() {
    statusDiv.textContent = 'Attempting to login...';
    chrome.identity.getAuthToken({ interactive: true }, function(token) {
      if (chrome.runtime.lastError) {
        console.error('Login failed:', chrome.runtime.lastError);
        statusDiv.textContent = 'Login failed: ' + chrome.runtime.lastError.message;
        return;
      }
      authToken = token;
      console.log('Login successful, token:', token);
      statusDiv.textContent = 'Login successful!';
      showMainContainer();
      fetchVideos();
    });
  });

  logoutButton.addEventListener('click', function() {
    if (authToken) {
      chrome.identity.removeCachedAuthToken({ token: authToken }, function() {
        authToken = null;
        showAuthContainer();
      });
    }
  });

  function showAuthContainer() {
    authContainer.style.display = 'block';
    mainContainer.style.display = 'none';
  }

  function showMainContainer() {
    authContainer.style.display = 'none';
    mainContainer.style.display = 'block';
  }

  function fetchVideos() {
    fetch('http://localhost:5119/api/videos', {
      headers: {
        'Authorization': 'Bearer ' + authToken
      }
    })
    .then(response => response.json())
    .then(videos => {
      videosList.innerHTML = '';
      videos.forEach(video => {
        const videoItem = document.createElement('div');
        videoItem.className = 'video-item';

        const videoName = document.createElement('span');
        videoName.textContent = video;
        videoItem.appendChild(videoName);

        const compressButton = document.createElement('button');
        compressButton.textContent = 'Compress';
        compressButton.addEventListener('click', function() {
          compressVideo(video);
        });
        videoItem.appendChild(compressButton);

        videosList.appendChild(videoItem);
      });
    })
    .catch(error => {
      statusDiv.textContent = 'Error fetching videos: ' + error.message;
    });
  }

  function compressVideo(mediaItemId) {
    statusDiv.textContent = `Compressing ${mediaItemId}...`;
    fetch(`http://localhost:5119/api/videos/${mediaItemId}/compress`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + authToken
      }
    })
    .then(response => response.json())
    .then(data => {
      statusDiv.textContent = data.message;
    })
    .catch(error => {
      statusDiv.textContent = 'Error compressing video: ' + error.message;
    });
  }
});

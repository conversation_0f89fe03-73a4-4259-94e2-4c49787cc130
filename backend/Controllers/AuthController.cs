using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using System.Text.Json;
using Google.Apis.Auth;
using VidCompressor.Models;

[ApiController]
[Route("[controller]")]
public class AuthController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly IConfiguration _configuration;
    private readonly IHttpClientFactory _httpClientFactory;

    public AuthController(ApplicationDbContext context, IConfiguration configuration, IHttpClientFactory httpClientFactory)
    {
        _context = context;
        _configuration = configuration;
        _httpClientFactory = httpClientFactory;
    }

    [HttpPost("signin")]
    [AllowAnonymous]
    public async Task<IActionResult> SignIn([FromBody] SignInRequest request)
    {
        try
        {
            Console.WriteLine($"Received sign-in request with token: {request.Token?.Substring(0, 20)}...");
            Console.WriteLine($"Google Client ID configured: {_configuration["Google:ClientId"]}");

            if (string.IsNullOrEmpty(request.Token))
            {
                Console.WriteLine("Error: Token is null or empty");
                return BadRequest(new { message = "Token is required" });
            }

            // Validate the access token with Google's userinfo endpoint
            var userInfo = await ValidateGoogleAccessTokenAsync(request.Token);
            if (userInfo == null)
            {
                Console.WriteLine("Error: Failed to validate Google access token");
                return BadRequest(new { message = "Invalid Google access token" });
            }

            Console.WriteLine($"Google token validated successfully for user: {userInfo.Email}");

            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == userInfo.Email);

            if (user == null)
            {
                Console.WriteLine($"Creating new user: {userInfo.Email}");
                user = new User
                {
                    Id = userInfo.Id,
                    Email = userInfo.Email,
                    SubscriptionStatus = "Free",
                    GoogleAccessToken = request.Token,
                    GoogleTokenExpiry = DateTime.UtcNow.AddHours(1) // Google tokens typically expire in 1 hour
                };
                _context.Users.Add(user);
            }
            else
            {
                Console.WriteLine($"Updating existing user: {userInfo.Email}");
                // Update the Google access token for existing user
                user.GoogleAccessToken = request.Token;
                user.GoogleTokenExpiry = DateTime.UtcNow.AddHours(1);
                _context.Users.Update(user);
            }

            await _context.SaveChangesAsync();
            Console.WriteLine("User saved to database successfully");

            var token = GenerateJwtToken(user);
            Console.WriteLine("JWT token generated successfully");

            return Ok(new { Token = token });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Authentication error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            return BadRequest(new { message = "Authentication failed: " + ex.Message });
        }
    }

    [HttpGet("me")]
    [Microsoft.AspNetCore.Authorization.Authorize]
    public async Task<IActionResult> GetCurrentUser()
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { message = "User not authenticated" });
            }

            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
            if (user == null)
            {
                return NotFound(new { message = "User not found" });
            }

            return Ok(new
            {
                id = user.Id,
                email = user.Email,
                subscriptionStatus = user.SubscriptionStatus
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "Error retrieving user: " + ex.Message });
        }
    }

    private async Task<GoogleUserInfo?> ValidateGoogleAccessTokenAsync(string accessToken)
    {
        try
        {
            Console.WriteLine($"Validating Google access token: {accessToken.Substring(0, 20)}...");

            var httpClient = _httpClientFactory.CreateClient();
            var url = $"https://www.googleapis.com/oauth2/v2/userinfo?access_token={accessToken}";
            Console.WriteLine($"Making request to: {url.Substring(0, 80)}...");

            var response = await httpClient.GetAsync(url);

            Console.WriteLine($"Google userinfo API response status: {response.StatusCode}");

            var responseContent = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"Google userinfo API response content: {responseContent}");

            if (!response.IsSuccessStatusCode)
            {
                Console.WriteLine($"Google userinfo API failed with status: {response.StatusCode}");
                Console.WriteLine($"Response headers: {response.Headers}");
                return null;
            }

            var userInfo = JsonSerializer.Deserialize<GoogleUserInfo>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Console.WriteLine($"Parsed user info - ID: {userInfo?.Id}, Email: {userInfo?.Email}");
            return userInfo;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error validating Google access token: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            return null;
        }
    }

    private string GenerateJwtToken(User user)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_configuration["Jwt:Secret"] ?? throw new InvalidOperationException("JWT Secret not configured"));
        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id),
                new Claim(ClaimTypes.Email, user.Email)
            }),
            Expires = DateTime.UtcNow.AddDays(7),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };
        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }
}

public class GoogleUserInfo
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Picture { get; set; } = string.Empty;
}

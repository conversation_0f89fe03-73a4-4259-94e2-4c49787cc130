using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StackExchange.Redis;
using System.Security.Claims;
using System.Threading.Tasks;
using VidCompressor.Models;
using VidCompressor.Services;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class VideosController : ControllerBase
{
    private readonly GooglePhotosService _googlePhotosService;
    private readonly IConnectionMultiplexer _redis;
    private readonly ApplicationDbContext _context;

    public VideosController(GooglePhotosService googlePhotosService, IConnectionMultiplexer redis, ApplicationDbContext context)
    {
        _googlePhotosService = googlePhotosService;
        _redis = redis;
        _context = context;
    }

    [HttpGet]
    public async Task<IActionResult> GetVideos()
    {
        var accessToken = await GetGoogleAccessTokenAsync();
        if (accessToken == null)
        {
            return Unauthorized(new { message = "Google access token not found or expired" });
        }

        var videos = await _googlePhotosService.GetVideosAsync(accessToken);
        return Ok(videos);
    }

    [HttpGet("{mediaItemId}/info")]
    public async Task<IActionResult> GetVideoInfo(string mediaItemId)
    {
        var accessToken = await GetGoogleAccessTokenAsync();
        if (accessToken == null)
        {
            return Unauthorized(new { message = "Google access token not found or expired" });
        }

        var videoInfo = await _googlePhotosService.GetVideoInfoAsync(accessToken, mediaItemId);
        return Ok(videoInfo);
    }

    [HttpGet("{mediaItemId}/download")]
    public async Task<IActionResult> DownloadVideo(string mediaItemId)
    {
        var accessToken = await GetGoogleAccessTokenAsync();
        if (accessToken == null)
        {
            return Unauthorized(new { message = "Google access token not found or expired" });
        }

        var videoStream = await _googlePhotosService.DownloadVideoAsync(accessToken, mediaItemId);
        return File(videoStream, "video/mp4", "video.mp4");
    }

    [HttpPost("{mediaItemId}/compress")]
    public async Task<IActionResult> CompressVideo(string mediaItemId, [FromBody] CompressVideoRequest request)
    {
        var accessToken = await GetGoogleAccessTokenAsync();
        if (accessToken == null)
        {
            return Unauthorized(new { message = "Google access token not found or expired" });
        }

        var quality = request?.Quality ?? "medium";
        var subscriber = _redis.GetSubscriber();
        await subscriber.PublishAsync(RedisChannel.Literal("video-compression-jobs"), $"{accessToken}:{mediaItemId}:{quality}");
        return Ok(new { message = "Video compression started" });
    }

    private async Task<string?> GetGoogleAccessTokenAsync()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return null;
        }

        var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null || string.IsNullOrEmpty(user.GoogleAccessToken))
        {
            return null;
        }

        // Check if token is expired
        if (user.GoogleTokenExpiry.HasValue && user.GoogleTokenExpiry.Value <= DateTime.UtcNow)
        {
            return null;
        }

        return user.GoogleAccessToken;
    }
}

public class CompressVideoRequest
{
    public string Quality { get; set; } = "medium";
}


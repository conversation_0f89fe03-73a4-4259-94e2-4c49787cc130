import React from 'react';
import { Button } from '@mui/material';
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe('YOUR_STRIPE_PUBLISHABLE_KEY');

const Subscribe: React.FC = () => {
  const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
    const stripe = await stripePromise;

    const response = await fetch('/api/payments/create-checkout-session', {
      method: 'POST',
    });

    const session = await response.json();

    if (stripe) {
      const result = await stripe.redirectToCheckout({
        sessionId: session.id,
      });

      if (result.error) {
        console.error(result.error.message);
      }
    }
  };

  return (
    <Button variant="contained" color="primary" onClick={handleClick}>
      Subscribe
    </Button>
  );
};

export default Subscribe;
